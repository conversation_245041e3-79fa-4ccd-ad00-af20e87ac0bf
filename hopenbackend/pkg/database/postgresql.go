package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// PostgreSQLClient wraps pgxpool.Pool with additional functionality
type PostgreSQLClient struct {
	Pool   *pgxpool.Pool
	logger *zap.Logger
	config *config.PostgreSQLConfig
}

// NewPostgreSQLClient creates a new PostgreSQL client
func NewPostgreSQLClient(cfg *config.PostgreSQLConfig, logger *zap.Logger) (*PostgreSQLClient, error) {
	// Configure connection pool
	poolConfig, err := pgxpool.ParseConfig(cfg.GetDSN())
	if err != nil {
		return nil, fmt.Errorf("failed to parse PostgreSQL config: %w", err)
	}

	// Set pool configuration
	poolConfig.MaxConns = cfg.MaxConnections
	poolConfig.MinConns = cfg.MinConnections
	poolConfig.MaxConnLifetime = cfg.MaxConnectionLifetime
	poolConfig.MaxConnIdleTime = cfg.MaxConnectionIdleTime
	poolConfig.HealthCheckPeriod = cfg.HealthCheckPeriod

	// Create connection pool
	pool, err := pgxpool.NewWithConfig(context.Background(), poolConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create PostgreSQL pool: %w", err)
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping PostgreSQL: %w", err)
	}

	logger.Info("PostgreSQL connection established",
		zap.String("host", cfg.Host),
		zap.Int("port", cfg.Port),
		zap.String("database", cfg.Database),
		zap.Int32("max_connections", cfg.MaxConnections),
	)

	return &PostgreSQLClient{
		Pool:   pool,
		logger: logger,
		config: cfg,
	}, nil
}

// Close closes the PostgreSQL connection pool
func (c *PostgreSQLClient) Close() {
	if c.Pool != nil {
		c.Pool.Close()
		c.logger.Info("PostgreSQL connection pool closed")
	}
}

// Health checks the health of the PostgreSQL connection
func (c *PostgreSQLClient) Health(ctx context.Context) error {
	return c.Pool.Ping(ctx)
}

// Stats returns connection pool statistics
func (c *PostgreSQLClient) Stats() *pgxpool.Stat {
	return c.Pool.Stat()
}

// InitializeSchema initializes the database schema
func (c *PostgreSQLClient) InitializeSchema(ctx context.Context) error {
    // BEST PRACTICE: A single string for a single atomic transaction.
    fullSchema := `
    BEGIN;

    -- 1. EXTENSIONS AND TYPES
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    DO $$ BEGIN CREATE TYPE bubble_status AS ENUM ('active', 'expired', 'archived'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE bubble_member_status AS ENUM ('active', 'left', 'removed', 'pending', 'accepted', 'declined'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE bubble_request_type AS ENUM ('invite', 'join', 'kick', 'start'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE request_status AS ENUM ('pending', 'approved', 'rejected', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE vote_type AS ENUM ('approve', 'reject'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE friend_request_status AS ENUM ('pending', 'accepted', 'declined', 'expired'); EXCEPTION WHEN duplicate_object THEN null; END $$;
    DO $$ BEGIN CREATE TYPE notification_type AS ENUM (
        'contactRequestDeclined',
        'bubbleJoinRequestRejected',
        'bubbleInviteRequestRejected',
        'bubbleVotekickPassed',
        'bubbleChatMessageReceived',
        'bubbleVoiceMessageReceived',
        'bubbleVideoMessageReceived',
        'bubbleAudioCallIncoming',
        'bubbleVideoCallIncoming',
        'bubbleScreenShareIncoming',
        'bubbleCallInProgress',
        'bubbleCallEnded',
        'bubbleMissedCall',
        'bubblePopReminder60Days',
        'bubblePopReminder30Days',
        'bubblePopReminder20Days',
        'bubblePopReminder10Days',
        'bubblePopReminder7Days',
        'bubblePopReminder3Days',
        'bubblePopReminder24Hours',
        'bubblePopped',
        'friendChatMessageReceived',
        'friendVoiceMessageReceived',
        'friendVideoMessageReceived',
        'friendAudioCallIncoming',
        'friendVideoCallIncoming',
        'friendScreenShareIncoming',
        'friendMissedCall',
        'friendCallInProgress',
        'inactiveNoBubble12Hours',
        'inactiveNoBubble1Day',
        'inactiveNoBubble2Days',
        'inactiveNoBubble3Days',
        'inactiveNoBubble7Days',
        'statusUpdates',
        'securityAlerts',
        'appUpdates',
        'bubble_expiring_soon',
        'bubble_is_full',
        'bubble_request_rejected',
        'bubble_start_request_received',
        'bubble_call_incoming',
        'bubble_call_in_progress',
        'bubble_call_ended',
        'bubble_call_missed',
        'inactive_no_bubble_12_hours',
        'inactive_no_bubble_24_hours',
        'inactive_no_bubble_3_days',
        'inactive_no_bubble_7_days',
        'system_notification'
    ); EXCEPTION WHEN duplicate_object THEN null; END $$;

    -- 2. TABLES
    CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        username VARCHAR(40) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        first_name VARCHAR(100) NOT NULL,
        last_name VARCHAR(100) NOT NULL,
        display_name VARCHAR(200) NOT NULL,
        avatar_bucket_name VARCHAR(100),
        avatar_object_key VARCHAR(500),
        date_of_birth DATE NOT NULL,
        is_premium BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        is_private BOOLEAN DEFAULT false,
        last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        presence_status VARCHAR(20) DEFAULT 'offline',
        notification_settings JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS bubbles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        creator_id UUID REFERENCES users(id) ON DELETE SET NULL,
        name VARCHAR(100) NOT NULL,
        capacity INTEGER NOT NULL DEFAULT 5 CHECK (capacity >= 2 AND capacity <= 5),
        member_count INTEGER NOT NULL DEFAULT 0 CHECK (member_count >= 0),
        status bubble_status DEFAULT 'active',
        expires_at TIMESTAMP WITH TIME ZONE,
        friend_request_on_expire BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS bubble_members (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        bubble_id UUID NOT NULL REFERENCES bubbles(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        status bubble_member_status DEFAULT 'active',
        joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        left_at TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(bubble_id, user_id, status)
    );

    CREATE TABLE IF NOT EXISTS notifications (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        type notification_type NOT NULL,
        grouping_key VARCHAR(255),
        title VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        data JSONB DEFAULT '{}',
        is_read BOOLEAN DEFAULT false,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        read_at TIMESTAMP WITH TIME ZONE,
        CONSTRAINT unique_unread_grouped_notifications
        UNIQUE (user_id, grouping_key)
        WHERE is_read = false AND grouping_key IS NOT NULL
    );

    CREATE TABLE IF NOT EXISTS media_files (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        filename VARCHAR(255) NOT NULL,
        original_filename VARCHAR(255) NOT NULL,
        content_type VARCHAR(100) NOT NULL,
        size_bytes BIGINT NOT NULL,
        bucket_name VARCHAR(100) NOT NULL,
        object_key VARCHAR(500) NOT NULL,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS call_sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
        initiator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        participants JSONB DEFAULT '[]',
        call_type VARCHAR(20) NOT NULL,
        status VARCHAR(20) DEFAULT 'initiated',
        started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        ended_at TIMESTAMP WITH TIME ZONE,
        duration_seconds INTEGER DEFAULT 0,
        metadata JSONB DEFAULT '{}'
    );

    CREATE TABLE IF NOT EXISTS bubble_requests (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        request_type bubble_request_type NOT NULL,
        bubble_id UUID REFERENCES bubbles(id) ON DELETE CASCADE,
        requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        target_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
        status request_status DEFAULT 'pending',
        message TEXT,
        requires_unanimous BOOLEAN DEFAULT true,
        expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        completed_at TIMESTAMP WITH TIME ZONE
    );

    CREATE TABLE IF NOT EXISTS request_votes (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        request_id UUID NOT NULL REFERENCES bubble_requests(id) ON DELETE CASCADE,
        voter_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        vote vote_type NOT NULL,
        voted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(request_id, voter_id)
    );

    CREATE TABLE IF NOT EXISTS friend_requests (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        requester_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        recipient_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        source_bubble_id UUID REFERENCES bubbles(id) ON DELETE SET NULL,
        auto_generated BOOLEAN DEFAULT false,
        status friend_request_status DEFAULT 'pending',
        message TEXT,
        expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        CONSTRAINT friend_requests_different_users CHECK (requester_id != recipient_id)
    );

    CREATE TABLE IF NOT EXISTS roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name VARCHAR(50) UNIQUE NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    CREATE TABLE IF NOT EXISTS user_roles (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
        is_active BOOLEAN DEFAULT true,
        assigned_by UUID REFERENCES users(id),
        assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(user_id, role_id)
    );

    CREATE TABLE IF NOT EXISTS user_relationships (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        from_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        to_user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        relationship_type VARCHAR(50) NOT NULL,
        status VARCHAR(20) DEFAULT 'active',
        created_by UUID REFERENCES users(id),
        reason TEXT,
        metadata JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE,
        CONSTRAINT user_relationships_different_users CHECK (from_user_id != to_user_id),
        CONSTRAINT user_relationships_valid_type CHECK (
            relationship_type IN ('ban', 'block', 'mute', 'restrict', 'follow', 'favorite')
        ),
        CONSTRAINT user_relationships_valid_status CHECK (
            status IN ('active', 'inactive', 'expired')
        )
    );

    -- 3. INDEXES
    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
    CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
    CREATE INDEX IF NOT EXISTS idx_users_is_private ON users(is_private);
    CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

    CREATE INDEX IF NOT EXISTS idx_bubbles_creator ON bubbles(creator_id);
    CREATE INDEX IF NOT EXISTS idx_bubbles_status ON bubbles(status);
    CREATE INDEX IF NOT EXISTS idx_bubbles_expires_at ON bubbles(expires_at);
    CREATE INDEX IF NOT EXISTS idx_bubbles_created_at ON bubbles(created_at);
    CREATE INDEX IF NOT EXISTS idx_bubbles_member_count ON bubbles(member_count);

    CREATE INDEX IF NOT EXISTS idx_bubble_members_bubble_id ON bubble_members(bubble_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id ON bubble_members(user_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_members_user_id_active ON bubble_members(user_id) WHERE status = 'active';
    CREATE INDEX IF NOT EXISTS idx_bubble_members_status ON bubble_members(status);

    CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_active_member
    ON bubble_members (bubble_id, user_id) WHERE status = 'active';

    CREATE INDEX IF NOT EXISTS idx_notifications_user_id_created_at
    ON notifications (user_id, created_at DESC);

    CREATE INDEX IF NOT EXISTS idx_notifications_user_id_is_read_created_at
    ON notifications (user_id, is_read, created_at DESC);

    CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);
    CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at);

    CREATE INDEX IF NOT EXISTS idx_notifications_grouping_key
    ON notifications(user_id, grouping_key) WHERE grouping_key IS NOT NULL;

    CREATE INDEX IF NOT EXISTS idx_media_files_user_id ON media_files(user_id);
    CREATE INDEX IF NOT EXISTS idx_media_files_content_type ON media_files(content_type);
    CREATE INDEX IF NOT EXISTS idx_media_files_created_at ON media_files(created_at);

    CREATE INDEX IF NOT EXISTS idx_call_sessions_bubble_id ON call_sessions(bubble_id);
    CREATE INDEX IF NOT EXISTS idx_call_sessions_initiator_id ON call_sessions(initiator_id);
    CREATE INDEX IF NOT EXISTS idx_call_sessions_status ON call_sessions(status);
    CREATE INDEX IF NOT EXISTS idx_call_sessions_started_at ON call_sessions(started_at);

    CREATE INDEX IF NOT EXISTS idx_bubble_requests_bubble_id ON bubble_requests(bubble_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_requester_id ON bubble_requests(requester_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_target_user_id ON bubble_requests(target_user_id);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_status ON bubble_requests(status);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_type ON bubble_requests(request_type);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_expires_at ON bubble_requests(expires_at);
    CREATE INDEX IF NOT EXISTS idx_bubble_requests_created_at ON bubble_requests(created_at);

    CREATE INDEX IF NOT EXISTS idx_request_votes_request_id ON request_votes(request_id);
    CREATE INDEX IF NOT EXISTS idx_request_votes_voter_id ON request_votes(voter_id);
    CREATE INDEX IF NOT EXISTS idx_request_votes_vote ON request_votes(vote);
    CREATE INDEX IF NOT EXISTS idx_request_votes_voted_at ON request_votes(voted_at);

    CREATE INDEX IF NOT EXISTS idx_friend_requests_requester_id ON friend_requests(requester_id);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_recipient_id ON friend_requests(recipient_id);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_source_bubble_id ON friend_requests(source_bubble_id);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_status ON friend_requests(status);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_auto_generated ON friend_requests(auto_generated);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_expires_at ON friend_requests(expires_at);
    CREATE INDEX IF NOT EXISTS idx_friend_requests_created_at ON friend_requests(created_at);

    -- 4. TRIGGER FUNCTIONS AND ATTACHMENTS
    CREATE OR REPLACE FUNCTION trigger_set_timestamp()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = NOW();
        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    CREATE TRIGGER set_timestamp_users
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_bubbles
        BEFORE UPDATE ON bubbles
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_bubble_members
        BEFORE UPDATE ON bubble_members
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_bubble_requests
        BEFORE UPDATE ON bubble_requests
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_friend_requests
        BEFORE UPDATE ON friend_requests
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_roles
        BEFORE UPDATE ON roles
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_user_roles
        BEFORE UPDATE ON user_roles
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE TRIGGER set_timestamp_user_relationships
        BEFORE UPDATE ON user_relationships
        FOR EACH ROW
        EXECUTE FUNCTION trigger_set_timestamp();

    CREATE OR REPLACE FUNCTION check_user_bubble_limit()
    RETURNS TRIGGER AS $$
    DECLARE
        user_is_premium BOOLEAN;
        current_bubble_count INTEGER;
        max_allowed_bubbles INTEGER;
    BEGIN
        IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.status != 'active' AND NEW.status = 'active') THEN
            SELECT is_premium INTO user_is_premium
            FROM users
            WHERE id = NEW.user_id;

            IF user_is_premium THEN
                max_allowed_bubbles := 5;  -- Premium users get 5 bubbles
            ELSE
                max_allowed_bubbles := 1;  -- Free users get 1 bubble
            END IF;

            SELECT COUNT(*) INTO current_bubble_count
            FROM bubble_members
            WHERE user_id = NEW.user_id
            AND status = 'active'
            AND bubble_id != NEW.bubble_id; -- Exclude current bubble to handle updates

            IF current_bubble_count >= max_allowed_bubbles THEN
                RAISE EXCEPTION 'User has reached maximum bubble limit. Free users: 1 bubble, Premium users: 5 bubbles. Current: %, Max: %',
                    current_bubble_count, max_allowed_bubbles
                    USING ERRCODE = 'P0001'; -- Custom error code for bubble limit
            END IF;
        END IF;

        RETURN NEW;
    END;
    $$ LANGUAGE plpgsql;

    DROP TRIGGER IF EXISTS enforce_bubble_limit ON bubble_members;
    CREATE TRIGGER enforce_bubble_limit
        BEFORE INSERT OR UPDATE ON bubble_members
        FOR EACH ROW
        EXECUTE FUNCTION check_user_bubble_limit();

    CREATE OR REPLACE FUNCTION update_bubble_member_count_for_log()
    RETURNS TRIGGER AS $$
    BEGIN
        IF TG_OP = 'INSERT' THEN
            IF NEW.status = 'active' THEN
                UPDATE bubbles SET member_count = member_count + 1 WHERE id = NEW.bubble_id;
            END IF;

        ELSIF TG_OP = 'DELETE' THEN
            IF OLD.status = 'active' THEN
                UPDATE bubbles SET member_count = member_count - 1 WHERE id = OLD.bubble_id;
            END IF;

        ELSIF TG_OP = 'UPDATE' THEN
            IF OLD.status = 'active' AND NEW.status <> 'active' THEN
                UPDATE bubbles SET member_count = member_count - 1 WHERE id = OLD.bubble_id;
            ELSIF OLD.status <> 'active' AND NEW.status = 'active' THEN
                UPDATE bubbles SET member_count = member_count + 1 WHERE id = NEW.bubble_id;
            END IF;
        END IF;

        IF (TG_OP = 'DELETE') THEN RETURN OLD; ELSE RETURN NEW; END IF;
    END;
    $$ LANGUAGE plpgsql;

    DROP TRIGGER IF EXISTS sync_member_count_log ON bubble_members;
    CREATE TRIGGER sync_member_count_log
        AFTER INSERT OR UPDATE OR DELETE ON bubble_members
        FOR EACH ROW
        EXECUTE FUNCTION update_bubble_member_count_for_log();

    UPDATE bubbles
    SET member_count = (
        SELECT COUNT(*)
        FROM bubble_members
        WHERE bubble_members.bubble_id = bubbles.id
        AND bubble_members.status = 'active'
    );

    COMMIT;
    `
    if _, err := c.Pool.Exec(ctx, fullSchema); err != nil {
        return fmt.Errorf("failed to execute schema transaction: %w", err)
    }

    c.logger.Info("PostgreSQL schema initialized successfully")
    return nil
}

// GetUserByID retrieves a user by ID
func (c *PostgreSQLClient) GetUserByID(ctx context.Context, userID string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url,
			   date_of_birth, is_active, is_private, is_banned, banned_at,
			   notification_settings, created_at, updated_at
		FROM users
		WHERE id = $1`

	var user User
	err := c.Pool.QueryRow(ctx, query, userID).Scan(
		&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
		&user.AvatarURL, &user.DateOfBirth, &user.IsActive, &user.IsPrivate,
		&user.IsBanned, &user.BannedAt, &user.NotificationSettings,
		&user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to get user by ID: %w", HandlePgxError(err, "users"))
	}

	// Initialize empty arrays for pending requests (these will be managed separately)
	user.PendingSentBubbleRequestUserIds = []string{}
	user.PendingReceivedBubbleRequestUserIds = []string{}
	user.PendingSentContactRequestIds = []string{}
	user.PendingReceivedContactRequestIds = []string{}

	return &user, nil
}

// CreateUser creates a new user
func (c *PostgreSQLClient) CreateUser(ctx context.Context, user *User) error {
	query := `
		INSERT INTO users (id, username, email, first_name, last_name, avatar_url,
						  date_of_birth, is_private, notification_settings)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING created_at, updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.Email, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
	).Scan(&user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create user: %w", HandlePgxError(err, "users"))
	}

	return nil
}

// SearchUsers searches for users by username, first name, or last name
func (c *PostgreSQLClient) SearchUsers(ctx context.Context, query string, limit int) ([]*User, error) {
	searchQuery := `
		SELECT id, username, email, first_name, last_name, avatar_url,
			   date_of_birth, is_active, is_private, notification_settings,
			   created_at, updated_at
		FROM users
		WHERE is_active = true
		AND is_private = false
		AND (username ILIKE $1 OR first_name ILIKE $1 OR last_name ILIKE $1)
		ORDER BY
			CASE
				WHEN username ILIKE $1 THEN 1
				WHEN first_name ILIKE $1 THEN 2
				WHEN last_name ILIKE $1 THEN 3
				ELSE 4
			END,
			username
		LIMIT $2`

	searchPattern := "%" + query + "%"
	rows, err := c.Pool.Query(ctx, searchQuery, searchPattern, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to search users: %w", HandlePgxError(err, "users"))
	}
	defer rows.Close()

	var users []*User
	for rows.Next() {
		var user User
		err := rows.Scan(
			&user.ID, &user.Username, &user.Email, &user.FirstName, &user.LastName,
			&user.AvatarURL, &user.DateOfBirth, &user.IsActive, &user.IsPrivate,
			&user.NotificationSettings, &user.CreatedAt, &user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan user: %w", HandlePgxError(err, "users"))
		}
		users = append(users, &user)
	}

	return users, nil
}

// UpdateUser updates an existing user
func (c *PostgreSQLClient) UpdateUser(ctx context.Context, user *User) error {
	query := `
		UPDATE users
		SET username = $2, first_name = $3, last_name = $4, avatar_url = $5,
		    date_of_birth = $6, is_private = $7, notification_settings = $8,
		    pending_sent_bubble_request_user_ids = $9,
		    pending_received_bubble_request_user_ids = $10,
		    pending_sent_contact_request_ids = $11,
		    pending_received_contact_request_ids = $12,
		    updated_at = NOW()
		WHERE id = $1
		RETURNING updated_at`

	err := c.Pool.QueryRow(ctx, query,
		user.ID, user.Username, user.FirstName, user.LastName,
		user.AvatarURL, user.DateOfBirth, user.IsPrivate, user.NotificationSettings,
		user.PendingSentBubbleRequestUserIds, user.PendingReceivedBubbleRequestUserIds,
		user.PendingSentContactRequestIds, user.PendingReceivedContactRequestIds,
	).Scan(&user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to update user: %w", HandlePgxError(err, "users"))
	}

	return nil
}

// UpdateUserPendingRequests updates only the pending request fields for a user
func (c *PostgreSQLClient) UpdateUserPendingRequests(ctx context.Context, userID string,
	pendingSentBubbleRequestUserIds, pendingReceivedBubbleRequestUserIds,
	pendingSentContactRequestIds, pendingReceivedContactRequestIds []string) error {

	query := `
		UPDATE users
		SET pending_sent_bubble_request_user_ids = $2,
		    pending_received_bubble_request_user_ids = $3,
		    pending_sent_contact_request_ids = $4,
		    pending_received_contact_request_ids = $5,
		    updated_at = NOW()
		WHERE id = $1`

	_, err := c.Pool.Exec(ctx, query,
		userID, pendingSentBubbleRequestUserIds, pendingReceivedBubbleRequestUserIds,
		pendingSentContactRequestIds, pendingReceivedContactRequestIds)

	if err != nil {
		return fmt.Errorf("failed to update user pending requests: %w", HandlePgxError(err, "users"))
	}

	return nil
}

// AddUserPendingSentBubbleRequest adds a user ID to the pending sent bubble requests list
func (c *PostgreSQLClient) AddUserPendingSentBubbleRequest(ctx context.Context, userID, targetUserID string) error {
	query := `
		UPDATE users
		SET pending_sent_bubble_request_user_ids = array_append(
			COALESCE(pending_sent_bubble_request_user_ids, '{}'), $2
		),
		updated_at = NOW()
		WHERE id = $1 AND NOT ($2 = ANY(COALESCE(pending_sent_bubble_request_user_ids, '{}')))`

	_, err := c.Pool.Exec(ctx, query, userID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to add pending sent bubble request: %w", HandlePgxError(err, "users"))
	}
	return nil
}

// RemoveUserPendingSentBubbleRequest removes a user ID from the pending sent bubble requests list
func (c *PostgreSQLClient) RemoveUserPendingSentBubbleRequest(ctx context.Context, userID, targetUserID string) error {
	query := `
		UPDATE users
		SET pending_sent_bubble_request_user_ids = array_remove(
			COALESCE(pending_sent_bubble_request_user_ids, '{}'), $2
		),
		updated_at = NOW()
		WHERE id = $1`

	_, err := c.Pool.Exec(ctx, query, userID, targetUserID)
	if err != nil {
		return fmt.Errorf("failed to remove pending sent bubble request: %w", HandlePgxError(err, "users"))
	}
	return nil
}

// SoftDeleteUser soft deletes a user by setting is_active to false
func (c *PostgreSQLClient) SoftDeleteUser(ctx context.Context, userID string) error {
	query := `
		UPDATE users
		SET is_active = false, updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to soft delete user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to soft delete user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil
}

// BanUser bans a user by setting is_banned to true
func (c *PostgreSQLClient) BanUser(ctx context.Context, userID string) error {
	query := `
		UPDATE users
		SET is_banned = true, banned_at = NOW(), updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to ban user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to ban user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil
}

// UnbanUser unbans a user by setting is_banned to false
func (c *PostgreSQLClient) UnbanUser(ctx context.Context, userID string) error {
	query := `
		UPDATE users
		SET is_banned = false, banned_at = NULL, updated_at = NOW()
		WHERE id = $1`

	result, err := c.Pool.Exec(ctx, query, userID)
	if err != nil {
		return fmt.Errorf("failed to unban user: %w", HandlePgxError(err, "users"))
	}

	if result.RowsAffected() == 0 {
		return fmt.Errorf("failed to unban user: %w", NewNotFoundError("users", "user not found"))
	}

	return nil
}

// GetUserByEmail retrieves a user by email address
func (c *PostgreSQLClient) GetUserByEmail(ctx context.Context, email string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth,
		       is_active, is_private, is_banned, banned_at, notification_settings,
		       created_at, updated_at
		FROM users
		WHERE email = $1 AND is_active = true`

	var user User
	err := c.Pool.QueryRow(ctx, query, email).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
		&user.DateOfBirth,
		&user.IsActive,
		&user.IsPrivate,
		&user.IsBanned,
		&user.BannedAt,
		&user.NotificationSettings,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by email: %w", err)
	}

	return &user, nil
}

// GetUserByUsername retrieves a user by username
func (c *PostgreSQLClient) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url, date_of_birth,
		       is_active, is_private, is_banned, banned_at, notification_settings,
		       created_at, updated_at
		FROM users
		WHERE username = $1 AND is_active = true`

	var user User
	err := c.Pool.QueryRow(ctx, query, username).Scan(
		&user.ID,
		&user.Username,
		&user.Email,
		&user.FirstName,
		&user.LastName,
		&user.AvatarURL,
		&user.DateOfBirth,
		&user.IsActive,
		&user.IsPrivate,
		&user.IsBanned,
		&user.BannedAt,
		&user.NotificationSettings,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user by username: %w", err)
	}

	return &user, nil
}

// User represents a user in the system
type User struct {
	ID                   string                 `json:"id"`
	Username             *string                `json:"username"`
	Email                string                 `json:"email"`
	FirstName            *string                `json:"first_name"`
	LastName             *string                `json:"last_name"`
	AvatarURL            *string                `json:"avatar_url"`
	DateOfBirth          *time.Time             `json:"date_of_birth"`
	IsActive             bool                   `json:"is_active"`
	IsPrivate            bool                   `json:"is_private"`
	IsBanned             bool                   `json:"is_banned"`
	BannedAt             *time.Time             `json:"banned_at"`
	NotificationSettings map[string]interface{} `json:"notification_settings"`

	// Pending request tracking fields
	PendingSentBubbleRequestUserIds     []string `json:"pending_sent_bubble_request_user_ids"`
	PendingReceivedBubbleRequestUserIds []string `json:"pending_received_bubble_request_user_ids"`
	PendingSentContactRequestIds        []string `json:"pending_sent_contact_request_ids"`
	PendingReceivedContactRequestIds    []string `json:"pending_received_contact_request_ids"`

	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
}

// BubbleRequest represents a bubble-related request (invite, join, kick, start)
type BubbleRequest struct {
	ID                string     `json:"id"`
	RequestType       string     `json:"request_type"`       // 'invite', 'join', 'kick', 'start'
	BubbleID          string     `json:"bubble_id"`
	RequesterID       string     `json:"requester_id"`       // User who initiated the request
	TargetUserID      *string    `json:"target_user_id"`     // User being invited/kicked (null for join)
	Status            string     `json:"status"`             // 'pending', 'approved', 'rejected', 'expired'
	Message           *string    `json:"message"`            // Optional message from requester
	RequiresUnanimous bool       `json:"requires_unanimous"` // Whether all members must approve
	ExpiresAt         time.Time  `json:"expires_at"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
	CompletedAt       *time.Time `json:"completed_at"`
}

// RequestVote represents an individual member's vote on a request
type RequestVote struct {
	ID        string    `json:"id"`
	RequestID string    `json:"request_id"`
	VoterID   string    `json:"voter_id"` // Member who is voting
	Vote      string    `json:"vote"`     // 'approve', 'reject'
	VotedAt   time.Time `json:"voted_at"`
	CreatedAt time.Time `json:"created_at"`
}
