# PostgreSQL Database Layer Documentation

**Author:** Gemini Code Assist
**Date:** 2023-10-27

## 1. Overview

This document provides a comprehensive technical overview of the `database` package, specifically the `postgresql.go` file. This package serves as the dedicated Data Access Layer (DAL) for all interactions with the PostgreSQL database.

The core design centers around the `PostgreSQLClient`, which encapsulates a `pgxpool` connection pool. This approach provides a robust, efficient, and centralized interface for all database operations, from initial connection and schema management to executing CRUD (Create, Read, Update, Delete) queries.

## 2. Core Components

### 2.1. `PostgreSQLClient` Struct

The `PostgreSQLClient` is the primary struct for all database interactions. It is designed to be instantiated once at application startup and shared as a dependency.

```go
type PostgreSQLClient struct {
	Pool   *pgxpool.Pool
	logger *zap.Logger
	config *config.PostgreSQLConfig
}
```

-   `Pool *pgxpool.Pool`: An instance of a `pgx/v5` connection pool. Utilizing a connection pool is a critical performance best practice. It mitigates the high latency of establishing new database connections by maintaining and reusing a set of open connections for concurrent requests.
-   `logger *zap.Logger`: An instance of the Zap structured logger. This is used for logging critical events such as successful connections, pool closure, and errors, providing essential observability.
-   `config *config.PostgreSQLConfig`: A reference to the PostgreSQL configuration struct, containing all parameters for connecting to and tuning the database pool.

### 2.2. `NewPostgreSQLClient(cfg, logger)`

This constructor function is the sole entry point for creating a `PostgreSQLClient`. Its execution flow is as follows:

1.  **Configuration Parsing**: It first calls `pgxpool.ParseConfig` to parse and validate the DSN (Data Source Name) from the configuration. This ensures the connection string is well-formed before proceeding.
2.  **Pool Tuning**: It then applies fine-grained tuning parameters to the pool configuration (`MaxConns`, `MinConns`, `MaxConnLifetime`, etc.). These settings are crucial for optimizing database resource utilization and application performance under various load profiles.
3.  **Pool Creation**: `pgxpool.NewWithConfig` creates the connection pool. It's important to note that this function does not necessarily establish all connections immediately; connections are typically created on-demand up to `MinConns`.
4.  **Connection Verification**: A `pool.Ping()` is performed with a 10-second timeout. This is a vital startup health check. It ensures the application fails fast if the database is unreachable, preventing it from starting in a broken state. If the ping fails, any partially created pool resources are cleaned up via `pool.Close()`.
5.  **Logging**: Upon success, it logs key connection details (host, port, database name), which is invaluable for debugging and operational monitoring.

## 3. Schema Management

### 3.1. `InitializeSchema(ctx)`

This method is responsible for idempotently setting up the entire database schema.

#### Design Philosophy

The entire schema is defined within a single, large SQL string and executed within an explicit transaction (`BEGIN; ... COMMIT;`). This has two key advantages:

1.  **Atomicity**: The entire schema setup is an all-or-nothing operation. If any statement fails, the transaction is rolled back, leaving the database in its previous state.
2.  **Idempotency**: The script heavily uses `CREATE ... IF NOT EXISTS` and `DO $$ ... EXCEPTION WHEN duplicate_object THEN null; END $$` blocks. This allows the initialization logic to be run safely on every application start. It will create missing objects on the first run and do nothing on subsequent runs.

#### Schema Breakdown

1.  **Extensions and Types**:
    -   `uuid-ossp`: Enabled to use `uuid_generate_v4()` for default primary key generation, a standard for modern applications.
    -   `ENUM` Types: Several custom `ENUM` types (e.g., `bubble_status`, `notification_type`) are created. This enforces data integrity at the database level, ensuring that status-like columns can only contain a predefined set of valid values.

2.  **Table Definitions**: The schema defines the core data models of the application, including `users`, `bubbles`, `bubble_members`, `notifications`, and `user_relationships`. The design employs modern relational database practices:
    -   `UUID` primary keys.
    -   Foreign key constraints with `ON DELETE` actions (`CASCADE`, `SET NULL`) to maintain relational integrity automatically.
    -   `CHECK` constraints to enforce business rules at the data layer (e.g., `bubbles.capacity` must be between 2 and 5).

3.  **Indexing Strategy**: A comprehensive indexing strategy is implemented to ensure high-performance queries.
    -   **Standard B-Tree Indexes**: On all foreign keys and frequently filtered columns (`WHERE` clauses).
    -   **Unique Indexes**: To enforce business uniqueness constraints (e.g., `users.username`).
    -   **Partial (Filtered) Indexes**: These are highly efficient as they only index a subset of rows that match a `WHERE` clause. A prime example is `CREATE UNIQUE INDEX IF NOT EXISTS idx_unique_active_member ON bubble_members (bubble_id, user_id) WHERE status = 'active';`. This efficiently enforces that a user can only be an *active* member of a bubble once.
    -   **Expression-based Indexes**: The `idx_unique_active_relationship` on `user_relationships` uses `LEAST(from_user_id, to_user_id)` and `GREATEST(from_user_id, to_user_id)`. This is a sophisticated technique to enforce a unique relationship between two users, regardless of which user is in the `from_user_id` or `to_user_id` column.

4.  **Triggers and Stored Procedures**: Business logic and maintenance tasks are automated using triggers.
    -   `trigger_set_timestamp()`: A classic "audit" trigger that automatically updates the `updated_at` column on any row modification. This is attached to most tables, abstracting this common task away from the application layer.
    -   `check_user_bubble_limit()`: This trigger enforces a critical business rule directly in the database: a user's limit on active bubbles (1 for free, 5 for premium). Placing this logic in the DB guarantees the rule can never be bypassed, providing a strong layer of integrity.
    -   `update_bubble_member_count_for_log()`: This trigger maintains a denormalized `member_count` on the `bubbles` table. When a member's status changes, the count is automatically adjusted. This denormalization improves read performance for fetching bubble lists (as it avoids a costly `COUNT(*)` subquery) at the expense of a minor write overhead. The final `UPDATE` statement in the schema script is a one-time backfill operation to correct counts for any pre-existing data.

## 4. Data Access Methods (User Management)

The file implements a standard set of CRUD operations for the `users` table.

-   **Read Operations** (`GetUserByID`, `GetUserByEmail`, `GetUserByUsername`): These are standard retrieval functions that use `pool.QueryRow().Scan()` to map a single database row to the `User` struct.
-   **Search Operation** (`SearchUsers`): Implements a flexible search using the `ILIKE` operator for case-insensitive pattern matching. The `ORDER BY CASE ... END` clause is a clever implementation detail that ranks results, prioritizing matches on `username` over other fields.
-   **Write Operations** (`CreateUser`, `UpdateUser`): These methods use the `RETURNING` clause to get database-generated values (like `updated_at`) back in a single round trip, which is more efficient than a subsequent `SELECT` query.
-   **State Change Operations** (`SoftDeleteUser`, `BanUser`, `UnbanUser`): These methods correctly implement state changes using flags (`is_active`, `is_banned`) rather than performing destructive `DELETE`s. This is a best practice for preserving user data history and allowing for account recovery. They also check `result.RowsAffected()` to ensure a record was actually updated, returning a `NotFoundError` if the target user doesn't exist.

## 5. Data Models

The file defines several Go structs (`User`, `BubbleRequest`, `ContactRequest`, etc.) that map to the database tables.

### Senior Engineer's Note on Architecture

While collocating data models with the DAL is convenient for small projects, it introduces tight coupling between the application's domain and the persistence layer.

**Recommendation:** For improved separation of concerns and adherence to Clean Architecture principles, these model structs should be relocated to a dedicated, neutral package (e.g., `pkg/models` or `pkg/domain`). This would allow other packages (e.g., `http` handlers, business logic services) to use these domain models without creating a dependency on the `database` package.

Furthermore, the `User` struct contains fields like `PendingSentBubbleRequestUserIds` which are initialized as empty slices but are not populated by any of the data access methods in this file. This implies they are populated at a higher application layer. This can be confusing for developers. A cleaner pattern would be for the DAL to return a "pure" `User` model, with a service layer being responsible for composing it with related data (like pending requests) from other DAL calls.

## 6. Error Handling

The data access methods demonstrate a solid error handling strategy:

1.  **Wrapping**: All errors returned from the `pgx` driver are wrapped with `fmt.Errorf("...: %w", err)`. This adds contextual information to the error while preserving the original error for potential inspection up the call stack.
2.  **Abstraction**: Errors are passed to a `HandlePgxError` utility function (defined elsewhere). This function is presumed to translate low-level `pgx` errors (e.g., `pgconn.PgError` with code `23505` for unique violation) into more expressive, application-specific errors (e.g., `ErrDuplicateEmail`). This abstraction simplifies error handling in the service layer.